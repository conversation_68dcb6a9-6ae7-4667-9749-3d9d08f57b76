#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parse all KL-Hotels-Real-*.md files and create hotels_updated.csv
with complete verified hotel data (1-897)
"""

import re
import csv
import os
from pathlib import Path

def clean_hotel_name(name):
    """Remove markdown formatting and clean hotel names"""
    if not name:
        return ""
    # Remove **bold** formatting
    name = re.sub(r'\*\*(.*?)\*\*', r'\1', name)
    # Remove extra whitespace
    name = re.sub(r'\s+', ' ', name).strip()
    return name

def extract_area_from_address(address):
    """Extract area/category from address"""
    if not address:
        return "Unknown"
    
    address_lower = address.lower()
    
    # Define area mappings
    area_mappings = {
        'klcc': 'KLCC',
        'bukit bintang': 'Bukit Bintang',
        'times square': 'Times Square',
        'chinatown': 'Chinatown',
        'sentral': 'KL Sentral',
        'pudu': 'Pudu',
        'ampang': 'Ampang',
        'imbi': 'Imb<PERSON>',
        'sultan ismail': '<PERSON>',
        'petaling jaya': 'Petaling Jaya',
        'damansara': '<PERSON><PERSON><PERSON>',
        'hartamas': 'Hartamas',
        'mont kiara': 'Mont Kiara',
        'cheras': 'Cheras',
        'brickfields': 'Brickfields',
        'tropicana': 'Tropicana',
        'kepong': 'Kepong',
        'sri petaling': 'Sri Petaling',
        'kampung attap': 'Kampung Attap'
    }
    
    for key, area in area_mappings.items():
        if key in address_lower:
            return area
    
    return "Kuala Lumpur"

def get_hotel_category(name, source):
    """Categorize hotel based on name and source"""
    name_lower = name.lower()
    
    if any(word in name_lower for word in ['hotel', 'resort', 'inn']):
        return 'Hotel'
    elif any(word in name_lower for word in ['residence', 'residences', 'suite', 'suites', 'apartment']):
        return 'Serviced Apartment'
    elif any(word in name_lower for word in ['hostel', 'space hotel']):
        return 'Hostel'
    else:
        return 'Accommodation'

def parse_markdown_file(file_path):
    """Parse a single markdown file and extract hotel data"""
    hotels = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all table rows
        table_pattern = r'\|\s*(\d+)\s*\|\s*\*\*(.*?)\*\*\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|'
        
        matches = re.findall(table_pattern, content, re.MULTILINE | re.IGNORECASE)
        
        for match in matches:
            try:
                serial = int(match[0].strip())
                hotel_name = clean_hotel_name(match[1].strip())
                phone = match[2].strip()
                email = match[3].strip()
                address = match[4].strip()
                source = match[5].strip()
                
                # Clean up phone and email
                phone = re.sub(r'\s+', ' ', phone).strip()
                email = re.sub(r'\s+', ' ', email).strip()
                address = re.sub(r'\s+', ' ', address).strip()
                
                # Extract area and category
                area = extract_area_from_address(address)
                category = get_hotel_category(hotel_name, source)
                
                hotels.append({
                    'No.': serial,
                    'Hotel Name': hotel_name,
                    'Phone': phone,
                    'Email': email,
                    'Address': address,
                    'Source': source,
                    'Category': category,
                    'Area': area
                })
                
            except (ValueError, IndexError) as e:
                print(f"Warning: Could not parse row in {file_path}: {match}")
                continue
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return hotels

def main():
    """Main function to parse all files and create CSV"""
    # Get all markdown files
    md_files = [
        'KL-Hotels-Verified-Contacts-Batch1-20.md',
        'KL-Hotels-Real-Verified-21-25.md',
        'KL-Hotels-Real-26-50-Complete.md',
        'KL-Hotels-Real-51-100-Complete.md',
        'KL-Hotels-Real-101-150-Complete.md',
        'KL-Hotels-Real-151-200-Complete.md',
        'KL-Hotels-Real-201-350-Complete.md',
        'KL-Hotels-Real-351-550-Complete.md',
        'KL-Hotels-Real-551-600-Complete.md',
        'KL-Hotels-Real-601-650-Complete.md',
        'KL-Hotels-Real-651-897-Final-Complete.md'
    ]
    
    all_hotels = []
    
    # Parse each file
    for md_file in md_files:
        file_path = Path(md_file)
        if file_path.exists():
            print(f"Parsing {md_file}...")
            hotels = parse_markdown_file(str(file_path))
            all_hotels.extend(hotels)
            print(f"  Found {len(hotels)} hotels")
        else:
            print(f"Warning: {md_file} not found")
    
    # Sort by serial number
    all_hotels.sort(key=lambda x: x['No.'])
    
    # Check for gaps in numbering
    expected_numbers = set(range(1, 898))
    actual_numbers = {hotel['No.'] for hotel in all_hotels}
    missing = expected_numbers - actual_numbers
    
    if missing:
        print(f"Warning: Missing hotel numbers: {sorted(missing)}")
    
    print(f"\nTotal hotels found: {len(all_hotels)}")
    print(f"Expected: 897")
    print(f"Actual: {len(all_hotels)}")
    
    # Create CSV file
    csv_file = 'hotels_updated.csv'
    fieldnames = ['No.', 'Hotel Name', 'Phone', 'Email', 'Address', 'Source', 'Category', 'Area']
    
    try:
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_hotels)
        
        print(f"\nSuccessfully created {csv_file} with {len(all_hotels)} hotels")
        
        # Print summary statistics
        categories = {}
        areas = {}
        for hotel in all_hotels:
            cat = hotel['Category']
            area = hotel['Area']
            categories[cat] = categories.get(cat, 0) + 1
            areas[area] = areas.get(area, 0) + 1
        
        print("\nCategory breakdown:")
        for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"  {cat}: {count}")
            
        print("\nTop areas:")
        for area, count in sorted(areas.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {area}: {count}")
            
    except Exception as e:
        print(f"Error creating CSV file: {e}")

if __name__ == "__main__":
    main()