#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create complete CSV file with all 897 verified hotels from available data sources
"""

import re
import csv
import os
from pathlib import Path

def clean_hotel_name(name):
    """Remove markdown formatting and clean hotel names"""
    if not name:
        return ""
    # Remove **bold** formatting
    name = re.sub(r'\*\*(.*?)\*\*', r'\1', name)
    # Remove extra whitespace
    name = re.sub(r'\s+', ' ', name).strip()
    return name

def extract_area_from_address(address):
    """Extract area/category from address"""
    if not address:
        return "Kuala Lumpur"
    
    address_lower = address.lower()
    
    # Define area mappings
    area_mappings = {
        'klcc': 'KLCC',
        'bukit bintang': 'Bukit Bintang',
        'times square': 'Times Square',
        'chinatown': 'Chinatown',
        'sentral': 'KL Sentral',
        'pudu': 'Pudu',
        'ampang': 'Ampang',
        'imbi': 'Imb<PERSON>',
        'sultan ismail': '<PERSON>',
        'petaling jaya': 'Petaling Jaya',
        'damansara': 'Dam<PERSON><PERSON>',
        'hartamas': 'Hartamas',
        'mont kiara': 'Mont Kiara',
        'cheras': 'Cheras',
        'brickfields': 'Brickfields',
        'tropicana': 'Tropicana',
        'kepong': 'Kepong',
        'sri petaling': 'Sri Petaling',
        'kampung attap': 'Kampung Attap',
        'setapak': 'Setapak',
        'wangsa maju': 'Wangsa Maju'
    }
    
    for key, area in area_mappings.items():
        if key in address_lower:
            return area
    
    return "Kuala Lumpur"

def get_hotel_category(name, source):
    """Categorize hotel based on name and source"""
    name_lower = name.lower()
    
    if any(word in name_lower for word in ['hotel', 'resort', 'inn', 'hostel']):
        return 'Hotel'
    elif any(word in name_lower for word in ['residence', 'residences', 'suite', 'suites', 'apartment', 'homestay']):
        return 'Serviced Apartment'
    else:
        return 'Accommodation'

def parse_all_hotels_extracted(file_path):
    """Parse the all_hotels_extracted.txt file"""
    hotels = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern for numbered entries
        pattern = r'(\d+)\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)'
        matches = re.findall(pattern, content, re.MULTILINE)
        
        for match in matches:
            try:
                serial = int(match[0].strip())
                hotel_name = clean_hotel_name(match[1].strip())
                phone = match[2].strip()
                email = match[3].strip()
                address = match[4].strip()
                source = match[5].strip()
                
                # Clean up data
                hotel_name = re.sub(r'\s+', ' ', hotel_name).strip()
                phone = re.sub(r'\s+', ' ', phone).strip()
                email = re.sub(r'\s+', ' ', email).strip()
                address = re.sub(r'\s+', ' ', address).strip()
                
                area = extract_area_from_address(address)
                category = get_hotel_category(hotel_name, source)
                
                hotels.append({
                    'No.': serial,
                    'Hotel Name': hotel_name,
                    'Phone': phone,
                    'Email': email,
                    'Address': address,
                    'Source': source,
                    'Category': category,
                    'Area': area
                })
                
            except (ValueError, IndexError) as e:
                print(f"Warning: Could not parse row: {match}")
                continue
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return hotels

def parse_markdown_files():
    """Parse all markdown files to get additional verified data"""
    md_files = [
        'KL-Hotels-Verified-Contacts-Batch1-20.md',
        'KL-Hotels-Real-Verified-21-25.md',
        'KL-Hotels-Real-26-50-Complete.md',
        'KL-Hotels-Real-51-100-Complete.md',
        'KL-Hotels-Real-101-150-Complete.md',
        'KL-Hotels-Real-151-200-Complete.md',
        'KL-Hotels-Real-201-350-Complete.md',
        'KL-Hotels-Real-351-550-Complete.md',
        'KL-Hotels-Real-551-600-Complete.md',
        'KL-Hotels-Real-601-650-Complete.md',
        'KL-Hotels-Real-651-897-Final-Complete.md'
    ]
    
    all_hotels = []
    
    for md_file in md_files:
        file_path = Path(md_file)
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all table rows
                table_pattern = r'\|\s*(\d+)\s*\|\s*\*\*(.*?)\*\*\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|'
                matches = re.findall(table_pattern, content, re.MULTILINE | re.IGNORECASE)
                
                for match in matches:
                    try:
                        serial = int(match[0].strip())
                        hotel_name = clean_hotel_name(match[1].strip())
                        phone = match[2].strip()
                        email = match[3].strip()
                        address = match[4].strip()
                        source = match[5].strip()
                        
                        # Clean up data
                        hotel_name = re.sub(r'\s+', ' ', hotel_name).strip()
                        phone = re.sub(r'\s+', ' ', phone).strip()
                        email = re.sub(r'\s+', ' ', email).strip()
                        address = re.sub(r'\s+', ' ', address).strip()
                        
                        area = extract_area_from_address(address)
                        category = get_hotel_category(hotel_name, source)
                        
                        all_hotels.append({
                            'No.': serial,
                            'Hotel Name': hotel_name,
                            'Phone': phone,
                            'Email': email,
                            'Address': address,
                            'Source': source,
                            'Category': category,
                            'Area': area
                        })
                        
                    except (ValueError, IndexError) as e:
                        continue
                        
            except Exception as e:
                print(f"Error reading {md_file}: {e}")
    
    return all_hotels

def build_complete_dataset():
    """Build complete dataset of 897 hotels"""
    
    # Get verified data
    verified_hotels = parse_all_hotels_extracted('all_hotels_extracted.txt')
    
    # Create a dictionary with hotel numbers as keys
    hotel_dict = {hotel['No.']: hotel for hotel in verified_hotels}
    
    # Create complete dataset of 897 hotels
    complete_hotels = []
    
    for i in range(1, 898):  # 1 to 897 inclusive
        if i in hotel_dict:
            # Use verified data
            complete_hotels.append(hotel_dict[i])
        else:
            # Create placeholder for missing hotel
            # Get hotel name from remaining-hotels.txt or other sources
            hotel_name = get_missing_hotel_name(i)
            
            complete_hotels.append({
                'No.': i,
                'Hotel Name': hotel_name or f'Hotel {i}',
                'Phone': 'To be verified',
                'Email': 'To be verified',
                'Address': 'Kuala Lumpur',
                'Source': 'Pending verification',
                'Category': 'Accommodation',
                'Area': 'Kuala Lumpur'
            })
    
    return complete_hotels

def get_missing_hotel_name(serial):
    """Get hotel name for missing serial numbers"""
    # Try to get from remaining-hotels.txt
    try:
        with open('remaining-hotels-551-897.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if 551 <= serial <= 897:
                index = serial - 551
                if index < len(lines):
                    return lines[index].strip()
    except:
        pass
    
    # Try to get from list.json structure
    return None

def main():
    """Main function to create complete CSV"""
    
    print("Building complete dataset of 897 hotels...")
    
    # Build complete dataset
    complete_hotels = build_complete_dataset()
    
    # Verify we have exactly 897 hotels
    print(f"Total hotels: {len(complete_hotels)}")
    
    # Check for duplicates
    serials = [h['No.'] for h in complete_hotels]
    duplicates = [x for x in serials if serials.count(x) > 1]
    if duplicates:
        print(f"Warning: Duplicate serial numbers: {duplicates}")
    
    # Check for missing numbers
    expected_serials = set(range(1, 898))
    actual_serials = set(serials)
    missing = expected_serials - actual_serials
    if missing:
        print(f"Warning: Missing serial numbers: {sorted(missing)}")
    
    # Create CSV file
    csv_file = 'hotels_updated.csv'
    fieldnames = ['No.', 'Hotel Name', 'Phone', 'Email', 'Address', 'Source', 'Category', 'Area']
    
    try:
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(complete_hotels)
        
        print(f"\n✅ Successfully created {csv_file} with {len(complete_hotels)} hotels")
        
        # Print summary statistics
        verified_count = sum(1 for h in complete_hotels if h['Source'] != 'Pending verification')
        pending_count = len(complete_hotels) - verified_count
        
        print(f"\n📊 Summary:")
        print(f"  Verified hotels: {verified_count}")
        print(f"  Pending verification: {pending_count}")
        
        # Category breakdown
        categories = {}
        for hotel in complete_hotels:
            cat = hotel['Category']
            categories[cat] = categories.get(cat, 0) + 1
        
        print(f"\n🏨 Category breakdown:")
        for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"  {cat}: {count}")
            
        # Area breakdown
        areas = {}
        for hotel in complete_hotels:
            area = hotel['Area']
            areas[area] = areas.get(area, 0) + 1
        
        print(f"\n📍 Top areas:")
        for area, count in sorted(areas.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {area}: {count}")
            
    except Exception as e:
        print(f"❌ Error creating CSV file: {e}")

if __name__ == "__main__":
    main()